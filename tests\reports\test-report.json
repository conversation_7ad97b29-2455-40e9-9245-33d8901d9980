{"suites": [{"name": "Unit Tests", "passed": false, "error": "Command failed: npx jest permanentProStatus.test.js --config=jest.config.js --verbose --json\nnpm WARN exec The following package was not found and will be installed: jest@30.0.4\nnpm WARN deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.\nnpm WARN deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported\n● Validation Error:\n\n  Test environment jest-environment-jsdom cannot be found. Make sure the testEnvironment configuration option points to an existing node module.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n\nAs of Jest 28 \"jest-environment-jsdom\" is no longer shipped by default, make sure to install it separately.\n", "duration": 0, "tests": 0, "failures": 1}, {"name": "Integration Tests", "passed": false, "error": "Command failed: npx jest integration.test.js --config=jest.config.js --verbose --json\n● Validation Error:\n\n  Test environment jest-environment-jsdom cannot be found. Make sure the testEnvironment configuration option points to an existing node module.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n\nAs of Jest 28 \"jest-environment-jsdom\" is no longer shipped by default, make sure to install it separately.\n", "duration": 0, "tests": 0, "failures": 1}, {"name": "Performance Tests", "passed": false, "error": "Command failed: npx jest performance.test.js --config=jest.config.js --verbose --json\n● Validation Error:\n\n  Test environment jest-environment-jsdom cannot be found. Make sure the testEnvironment configuration option points to an existing node module.\n\n  Configuration Documentation:\n  https://jestjs.io/docs/configuration\n\n\nAs of Jest 28 \"jest-environment-jsdom\" is no longer shipped by default, make sure to install it separately.\n", "duration": 0, "tests": 0, "failures": 1}], "performance": {}, "coverage": null, "summary": {"total": 0, "passed": 0, "failed": 3, "duration": 25775}}