// Unit Tests for Permanent Pro Status Storage System
// Tests permanent storage operations, migration, and validation flows

import { 
    storePermanentProStatus, 
    checkPermanentProStatus, 
    removePermanentProStatus, 
    migrateCacheToPermanent,
    getAllPermanentProStatuses,
    clearAllPermanentProStatuses
} from '../js/auth/permanentProStatus.js';

// Mock Chrome Storage API
const mockChromeStorage = {
    sync: {
        data: {},
        get: jest.fn((keys) => {
            if (Array.isArray(keys)) {
                const result = {};
                keys.forEach(key => {
                    if (mockChromeStorage.sync.data[key]) {
                        result[key] = mockChromeStorage.sync.data[key];
                    }
                });
                return Promise.resolve(result);
            } else if (typeof keys === 'object') {
                const result = {};
                Object.keys(keys).forEach(key => {
                    result[key] = mockChromeStorage.sync.data[key] || keys[key];
                });
                return Promise.resolve(result);
            } else {
                return Promise.resolve({ [keys]: mockChromeStorage.sync.data[keys] });
            }
        }),
        set: jest.fn((data) => {
            Object.assign(mockChromeStorage.sync.data, data);
            return Promise.resolve();
        }),
        remove: jest.fn((keys) => {
            if (Array.isArray(keys)) {
                keys.forEach(key => delete mockChromeStorage.sync.data[key]);
            } else {
                delete mockChromeStorage.sync.data[keys];
            }
            return Promise.resolve();
        })
    },
    local: {
        data: {},
        get: jest.fn((keys) => {
            if (Array.isArray(keys)) {
                const result = {};
                keys.forEach(key => {
                    if (mockChromeStorage.local.data[key]) {
                        result[key] = mockChromeStorage.local.data[key];
                    }
                });
                return Promise.resolve(result);
            } else {
                return Promise.resolve(mockChromeStorage.local.data);
            }
        }),
        set: jest.fn((data) => {
            Object.assign(mockChromeStorage.local.data, data);
            return Promise.resolve();
        })
    }
};

// Mock hashKey function
jest.mock('../js/security/hashUtils.js', () => ({
    hashKey: jest.fn((key) => Promise.resolve('abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'))
}));

// Setup global chrome mock
global.chrome = {
    storage: mockChromeStorage
};

describe('Permanent Pro Status Storage System', () => {
    beforeEach(() => {
        // Clear all mock data before each test
        mockChromeStorage.sync.data = {};
        mockChromeStorage.local.data = {};
        jest.clearAllMocks();
    });

    describe('storePermanentProStatus', () => {
        test('should store permanent Pro status successfully', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = {
                tier: 'pro',
                status: 'active',
                expiresAt: '2026-01-15T10:30:00.000Z',
                createdAt: '2025-01-15T10:30:00.000Z',
                usageCount: 5
            };

            const result = await storePermanentProStatus(keyHash, membershipDetails);

            expect(result).toBe(true);
            expect(mockChromeStorage.sync.set).toHaveBeenCalledWith({
                'permanentProStatus_abcdef1234567890': expect.objectContaining({
                    isPro: true,
                    verified: true,
                    permanent: true,
                    keyHash: 'abcdef1234567890',
                    membershipDetails: expect.objectContaining({
                        tier: 'pro',
                        status: 'active',
                        expiresAt: '2026-01-15T10:30:00.000Z'
                    })
                })
            });
        });

        test('should handle storage errors gracefully', async () => {
            mockChromeStorage.sync.set.mockRejectedValueOnce(new Error('Storage quota exceeded'));
            
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = { tier: 'pro', status: 'active' };

            const result = await storePermanentProStatus(keyHash, membershipDetails);

            expect(result).toBe(false);
        });
    });

    describe('checkPermanentProStatus', () => {
        test('should return verified status for existing permanent Pro user', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup existing permanent status
            mockChromeStorage.sync.data['permanentProStatus_abcdef1234567890'] = {
                isPro: true,
                verified: true,
                permanent: true,
                verifiedAt: '2025-01-15T10:30:00.000Z',
                keyHash: 'abcdef1234567890',
                membershipDetails: {
                    tier: 'pro',
                    status: 'active',
                    expiresAt: '2026-01-15T10:30:00.000Z'
                }
            };

            const result = await checkPermanentProStatus(keyHash);

            expect(result.verified).toBe(true);
            expect(result.isPro).toBe(true);
            expect(result.message).toBe('Pro status verified (permanent)');
            expect(result.permanent).toBe(true);
        });

        test('should return not verified for non-existent permanent status', async () => {
            const keyHash = 'nonexistent1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

            const result = await checkPermanentProStatus(keyHash);

            expect(result.verified).toBe(false);
            expect(result.isPro).toBe(false);
            expect(result.message).toBe('No permanent Pro status found');
        });

        test('should handle expired permanent status', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            
            // Setup expired permanent status
            mockChromeStorage.sync.data['permanentProStatus_abcdef1234567890'] = {
                isPro: true,
                verified: true,
                permanent: true,
                verifiedAt: '2025-01-15T10:30:00.000Z',
                keyHash: 'abcdef1234567890',
                membershipDetails: {
                    tier: 'pro',
                    status: 'active',
                    expiresAt: '2024-01-15T10:30:00.000Z' // Expired
                }
            };

            const result = await checkPermanentProStatus(keyHash);

            expect(result.verified).toBe(false);
            expect(result.isPro).toBe(false);
            expect(result.message).toBe('Permanent Pro status expired');
            expect(result.expired).toBe(true);
        });
    });

    describe('removePermanentProStatus', () => {
        test('should remove permanent Pro status successfully', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

            const result = await removePermanentProStatus(keyHash);

            expect(result).toBe(true);
            expect(mockChromeStorage.sync.remove).toHaveBeenCalledWith(['permanentProStatus_abcdef1234567890']);
        });

        test('should handle removal errors gracefully', async () => {
            mockChromeStorage.sync.remove.mockRejectedValueOnce(new Error('Storage error'));
            
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';

            const result = await removePermanentProStatus(keyHash);

            expect(result).toBe(false);
        });
    });

    describe('migrateCacheToPermanent', () => {
        test('should migrate from cache to permanent storage successfully', async () => {
            // Setup existing Pro key and status
            mockChromeStorage.sync.data = {
                hustleProKey: 'test-pro-key-123',
                hustleProStatus: {
                    isPro: true,
                    lastChecked: '2025-01-15T10:30:00.000Z'
                }
            };

            // Setup cached data
            mockChromeStorage.local.data = {
                'proStatus_abcdef1234567890': {
                    isPro: true,
                    lastValidated: '2025-01-15T10:30:00.000Z',
                    membershipDetails: {
                        tier: 'pro',
                        status: 'active'
                    }
                }
            };

            const result = await migrateCacheToPermanent();

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(true);
            expect(result.migratedFrom).toBe('cache');
        });

        test('should not migrate if no Pro key exists', async () => {
            const result = await migrateCacheToPermanent();

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(false);
            expect(result.message).toBe('No Pro key found, migration not needed');
        });

        test('should not migrate if permanent status already exists', async () => {
            // Setup existing Pro key
            mockChromeStorage.sync.data = {
                hustleProKey: 'test-pro-key-123',
                hustleProStatus: { isPro: true }
            };

            // Setup existing permanent status
            mockChromeStorage.sync.data['permanentProStatus_abcdef1234567890'] = {
                isPro: true,
                verified: true,
                permanent: true
            };

            const result = await migrateCacheToPermanent();

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(false);
            expect(result.message).toBe('Permanent status already exists');
        });
    });

    describe('getAllPermanentProStatuses', () => {
        test('should return all permanent Pro status entries', async () => {
            mockChromeStorage.sync.data = {
                'permanentProStatus_abcdef1234567890': {
                    isPro: true,
                    verified: true,
                    permanent: true,
                    keyHash: 'abcdef1234567890'
                },
                'permanentProStatus_fedcba0987654321': {
                    isPro: true,
                    verified: true,
                    permanent: true,
                    keyHash: 'fedcba0987654321'
                },
                'otherData': 'should be ignored'
            };

            const result = await getAllPermanentProStatuses();

            expect(result).toHaveLength(2);
            expect(result[0].keyHash).toBe('abcdef1234567890');
            expect(result[1].keyHash).toBe('fedcba0987654321');
        });
    });

    describe('clearAllPermanentProStatuses', () => {
        test('should clear all permanent Pro status entries', async () => {
            mockChromeStorage.sync.data = {
                'permanentProStatus_abcdef1234567890': { permanent: true },
                'permanentProStatus_fedcba0987654321': { permanent: true },
                'otherData': 'should remain'
            };

            const result = await clearAllPermanentProStatuses();

            expect(result).toBe(true);
            expect(mockChromeStorage.sync.remove).toHaveBeenCalledWith([
                'permanentProStatus_abcdef1234567890',
                'permanentProStatus_fedcba0987654321'
            ]);
        });
    });
});

// Performance Tests
describe('Performance Tests', () => {
    test('permanent status check should be fast', async () => {
        const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
        
        // Setup permanent status
        mockChromeStorage.sync.data['permanentProStatus_abcdef1234567890'] = {
            isPro: true,
            verified: true,
            permanent: true
        };

        const startTime = performance.now();
        await checkPermanentProStatus(keyHash);
        const endTime = performance.now();

        // Should complete in under 100ms (target from PRP)
        expect(endTime - startTime).toBeLessThan(100);
    });
});
