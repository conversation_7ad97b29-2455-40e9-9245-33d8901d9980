// Manual Method Comparison Script
// Based on analysis of popup-original.js vs current refactored version

console.log('🔍 Starting manual method comparison based on original popup.js analysis...');

// Complete list of methods from original popup.js (extracted manually)
const ORIGINAL_METHODS = {
    // Core initialization and setup
    'constructor': { params: [], type: 'constructor', critical: true },
    'init': { params: [], type: 'async', critical: true },
    'loadApiKey': { params: [], type: 'async', critical: true },
    'saveApiKey': { params: ['apiKey'], type: 'async', critical: true },
    'updateUI': { params: [], type: 'sync', critical: true },
    'updateApiStatus': { params: [], type: 'sync', critical: true },
    
    // UI Management
    'showSection': { params: ['sectionId'], type: 'sync', critical: true },
    'showError': { params: ['message'], type: 'sync', critical: true },
    'showSuccess': { params: ['message'], type: 'sync', critical: true },
    'showMembershipWarning': { params: ['warning'], type: 'sync', critical: true },
    'showHelp': { params: [], type: 'sync', critical: false },
    'showAbout': { params: [], type: 'sync', critical: false },
    'escapeHtml': { params: ['text'], type: 'sync', critical: false },
    
    // Core Analysis Functions
    'analyzeSelection': { params: [], type: 'async', critical: true },
    'analyzePage': { params: [], type: 'async', critical: true },
    'runCustomAnalysis': { params: [], type: 'async', critical: true },
    'performAnalysis': { params: ['prompt', 'analysisType'], type: 'async', critical: true },
    'displayResults': { params: ['result', 'analysisType', 'date'], type: 'sync', critical: true },
    'formatAnalysisContent': { params: ['content', 'summary'], type: 'sync', critical: false },
    'formatToolCalls': { params: ['toolCalls'], type: 'sync', critical: false },
    'convertToMarkdown': { params: ['analysisData'], type: 'sync', critical: false },
    'copyResults': { params: [], type: 'async', critical: false },
    'exportResults': { params: [], type: 'sync', critical: false },
    'analyzeSelectionWithText': { params: ['selectedText'], type: 'async', critical: true },
    'analyzePageWithData': { params: ['pageData'], type: 'async', critical: true },
    
    // Data Management
    'saveAnalysis': { params: ['analysisType', 'result'], type: 'async', critical: true },
    'loadAndDisplayAnalysis': { params: [], type: 'async', critical: true },
    'viewAnalysisFromHistory': { params: ['analysisId'], type: 'async', critical: true },
    'deleteAnalysis': { params: ['analysisId'], type: 'async', critical: true },
    
    // Pro Features and Settings
    'handleCustomAnalysisClick': { params: [], type: 'async', critical: true },
    'handleProKeyValidation': { params: [], type: 'async', critical: true },
    'updateProKeyStatus': { params: ['status', 'message'], type: 'sync', critical: true },
    'handlePromptManagerClick': { params: [], type: 'async', critical: true },
    'loadSettingsContent': { params: [], type: 'async', critical: true },
    'loadMembershipInfo': { params: [], type: 'async', critical: true },
    
    // Prompt Management
    'loadPromptManagement': { params: [], type: 'async', critical: true },
    'loadSavedPromptsSelect': { params: [], type: 'async', critical: true },
    'loadSelectedPrompt': { params: [], type: 'async', critical: true },
    'saveCurrentPrompt': { params: [], type: 'async', critical: true },
    'refreshPromptList': { params: [], type: 'async', critical: true },
    'displayPromptList': { params: ['prompts', 'pagination'], type: 'sync', critical: true },
    'searchPrompts': { params: ['query'], type: 'async', critical: true },
    'sortPrompts': { params: ['sortBy'], type: 'async', critical: true },
    'loadTagFilters': { params: [], type: 'async', critical: true },
    'filterByTag': { params: ['tag'], type: 'async', critical: true },
    'usePrompt': { params: ['promptId'], type: 'async', critical: true },
    'togglePromptPin': { params: ['promptId'], type: 'async', critical: true },
    'openPromptEditor': { params: ['prompt'], type: 'async', critical: true },
    'editPrompt': { params: ['promptId'], type: 'async', critical: true },
    'closePromptEditor': { params: [], type: 'sync', critical: true },
    'savePromptEdit': { params: [], type: 'async', critical: true },
    'deletePromptConfirm': { params: ['promptId'], type: 'async', critical: true },
    'deletePrompt': { params: ['promptId'], type: 'async', critical: true },
    'copyPromptContent': { params: ['promptId'], type: 'async', critical: true },
    'exportPrompts': { params: [], type: 'async', critical: true },
    'importPrompts': { params: ['file'], type: 'async', critical: true },
    
    // Integration Settings
    'loadTelegramSettings': { params: [], type: 'async', critical: true },
    'loadDiscordSettings': { params: [], type: 'async', critical: true },
    'setupTelegramEventListeners': { params: [], type: 'sync', critical: true },
    'setupDiscordEventListeners': { params: [], type: 'sync', critical: true },
    'saveTelegramSettings': { params: [], type: 'async', critical: true },
    'testTelegramConnection': { params: [], type: 'async', critical: true },
    'editTelegramSettings': { params: [], type: 'async', critical: true },
    'clearTelegramSettings': { params: [], type: 'async', critical: true },
    'saveDiscordSettings': { params: [], type: 'async', critical: true },
    'testDiscordConnection': { params: [], type: 'async', critical: true },
    'editDiscordSettings': { params: [], type: 'async', critical: true },
    'clearDiscordSettings': { params: [], type: 'async', critical: true },
    'handleTelegramAutoSendToggle': { params: ['enabled'], type: 'async', critical: true },
    'handleDiscordAutoSendToggle': { params: ['enabled'], type: 'async', critical: true },
    
    // Auto-send functionality
    'handleAutoSend': { params: ['analysisType', 'result'], type: 'async', critical: true },
    'handleTelegramAutoSend': { params: ['analysisData'], type: 'async', critical: true },
    'handleDiscordAutoSend': { params: ['analysisData'], type: 'async', critical: true },
    
    // Event handling
    'setupEventListeners': { params: [], type: 'sync', critical: true },
    'setupMessageListeners': { params: [], type: 'sync', critical: true },
    'setupEventDelegation': { params: [], type: 'sync', critical: true },
    'checkPendingAnalysis': { params: [], type: 'async', critical: true },
    'checkContextMenuActions': { params: [], type: 'async', critical: true },
    
    // Key management
    'showKeyManagementModal': { params: [], type: 'async', critical: true },
    'setupKeyManagementEventListeners': { params: [], type: 'sync', critical: true },
    'handleNewKeyValidation': { params: [], type: 'async', critical: true },
    'updateNewKeyStatus': { params: ['status', 'message'], type: 'sync', critical: true },
    'closeKeyManagementModal': { params: [], type: 'sync', critical: true },
    
    // Integration sending
    'sendAnalysisToTelegram': { params: ['analysisId'], type: 'async', critical: true },
    'sendAnalysisToDiscord': { params: ['analysisId'], type: 'async', critical: true }
};

function checkMethodImplementation() {
    console.log('🔍 Checking method implementation in current refactored version...');
    
    const results = {
        implemented: [],
        missing: [],
        delegated: [],
        criticalMissing: [],
        errors: []
    };
    
    if (!window.analyzer) {
        results.errors.push('No analyzer instance found');
        return results;
    }
    
    for (const [methodName, methodInfo] of Object.entries(ORIGINAL_METHODS)) {
        try {
            // Check if method exists directly on analyzer
            if (typeof window.analyzer[methodName] === 'function') {
                results.implemented.push({
                    method: methodName,
                    location: 'direct',
                    critical: methodInfo.critical,
                    type: methodInfo.type,
                    params: methodInfo.params
                });
                continue;
            }
            
            // Check if method exists on controller managers
            let found = false;
            const managers = [
                'analysisManager',
                'dataManager', 
                'uiManager',
                'settingsManager',
                'promptUIManager',
                'integrationManager',
                'autoSendManager',
                'eventManager'
            ];
            
            for (const manager of managers) {
                if (window.analyzer.controller?.[manager]?.[methodName]) {
                    results.delegated.push({
                        method: methodName,
                        location: manager,
                        critical: methodInfo.critical,
                        type: methodInfo.type,
                        params: methodInfo.params
                    });
                    found = true;
                    break;
                }
            }
            
            if (!found) {
                results.missing.push({
                    method: methodName,
                    critical: methodInfo.critical,
                    type: methodInfo.type,
                    params: methodInfo.params
                });
                
                if (methodInfo.critical) {
                    results.criticalMissing.push(methodName);
                }
            }
            
        } catch (error) {
            results.errors.push(`Error checking ${methodName}: ${error.message}`);
        }
    }
    
    return results;
}

function generateCompatibilityReport() {
    console.log('📊 Generating compatibility report...');
    
    const results = checkMethodImplementation();
    const totalMethods = Object.keys(ORIGINAL_METHODS).length;
    const implementedCount = results.implemented.length + results.delegated.length;
    const compatibilityPercentage = Math.round((implementedCount / totalMethods) * 100);
    
    console.log('📋 COMPATIBILITY REPORT');
    console.log('='.repeat(50));
    console.log(`📊 Total Original Methods: ${totalMethods}`);
    console.log(`✅ Implemented/Delegated: ${implementedCount}`);
    console.log(`❌ Missing: ${results.missing.length}`);
    console.log(`🔴 Critical Missing: ${results.criticalMissing.length}`);
    console.log(`📈 Compatibility: ${compatibilityPercentage}%`);
    console.log('');
    
    if (results.implemented.length > 0) {
        console.log('✅ DIRECTLY IMPLEMENTED METHODS:');
        results.implemented.forEach(item => {
            const criticalMark = item.critical ? '🔴' : '🟢';
            console.log(`  ${criticalMark} ${item.method}(${item.params.join(', ')})`);
        });
        console.log('');
    }
    
    if (results.delegated.length > 0) {
        console.log('🔄 DELEGATED METHODS:');
        const byManager = {};
        results.delegated.forEach(item => {
            if (!byManager[item.location]) byManager[item.location] = [];
            byManager[item.location].push(item);
        });
        
        for (const [manager, methods] of Object.entries(byManager)) {
            console.log(`  📁 ${manager}: ${methods.length} methods`);
            methods.forEach(item => {
                const criticalMark = item.critical ? '🔴' : '🟢';
                console.log(`    ${criticalMark} ${item.method}(${item.params.join(', ')})`);
            });
        }
        console.log('');
    }
    
    if (results.missing.length > 0) {
        console.log('❌ MISSING METHODS:');
        results.missing.forEach(item => {
            const criticalMark = item.critical ? '🔴 CRITICAL' : '🟡 NON-CRITICAL';
            console.log(`  ${criticalMark} ${item.method}(${item.params.join(', ')})`);
        });
        console.log('');
    }
    
    if (results.criticalMissing.length > 0) {
        console.log('🚨 CRITICAL MISSING METHODS (MUST FIX):');
        results.criticalMissing.forEach(method => {
            console.log(`  🔴 ${method}`);
        });
        console.log('');
    }
    
    if (results.errors.length > 0) {
        console.log('⚠️ ERRORS:');
        results.errors.forEach(error => console.log(`  ❌ ${error}`));
        console.log('');
    }
    
    // Recommendations
    console.log('💡 RECOMMENDATIONS:');
    if (compatibilityPercentage >= 95) {
        console.log('  🎉 Excellent compatibility! Refactoring is successful.');
    } else if (compatibilityPercentage >= 85) {
        console.log('  ✅ Good compatibility. Address missing methods if needed.');
    } else {
        console.log('  ⚠️ Low compatibility. Significant work needed.');
    }
    
    if (results.criticalMissing.length > 0) {
        console.log('  🚨 URGENT: Fix critical missing methods immediately!');
    }
    
    if (results.missing.length > results.criticalMissing.length) {
        console.log('  📝 Consider implementing non-critical missing methods for full compatibility.');
    }
    
    // Store results globally
    window.compatibilityResults = {
        results,
        totalMethods,
        implementedCount,
        compatibilityPercentage,
        originalMethods: ORIGINAL_METHODS
    };
    
    return window.compatibilityResults;
}

// Auto-run the comparison
setTimeout(() => {
    generateCompatibilityReport();
    console.log('\n💾 Results stored in window.compatibilityResults');
}, 1000);
