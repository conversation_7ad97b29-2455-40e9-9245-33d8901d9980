// Integration Tests for Permanent Pro Status System
// Tests the complete validation flow with permanent storage

import { validatePro<PERSON>ey } from '../js/auth/proValidator.js';
import { checkProStatus } from '../js/user/proStatus.js';
import { 
    storePermanentProStatus, 
    checkPermanentProStatus, 
    migrateCacheToPermanent 
} from '../js/auth/permanentProStatus.js';

// Mock the hash function
jest.mock('../js/security/hashUtils.js', () => ({
    hashKey: jest.fn((key) => Promise.resolve('abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890'))
}));

// Mock the API validation functions
jest.mock('../js/auth/proValidator.js', () => {
    const originalModule = jest.requireActual('../js/auth/proValidator.js');
    return {
        ...originalModule,
        performValidation: jest.fn(),
        getCachedProStatus: jest.fn()
    };
});

describe('Integration Tests - Complete Validation Flow', () => {
    beforeEach(() => {
        // Reset Chrome storage mock
        chrome.storage.sync.get.mockImplementation((keys) => {
            const data = {};
            if (Array.isArray(keys)) {
                keys.forEach(key => {
                    data[key] = undefined;
                });
            }
            return Promise.resolve(data);
        });
        
        chrome.storage.sync.set.mockImplementation(() => Promise.resolve());
        chrome.storage.local.get.mockImplementation(() => Promise.resolve({}));
    });

    describe('First-time Pro User Validation', () => {
        test('should validate Pro key and store permanent status', async () => {
            // Mock successful API validation
            const mockValidationResult = {
                isPro: true,
                cached: false,
                message: 'Pro user verified',
                membershipDetails: {
                    tier: 'pro',
                    status: 'active',
                    expiresAt: '2026-01-15T10:30:00.000Z',
                    createdAt: '2025-01-15T10:30:00.000Z',
                    usageCount: 5
                }
            };

            // Mock the performValidation function to return success
            const { performValidation } = require('../js/auth/proValidator.js');
            performValidation.mockResolvedValueOnce(mockValidationResult);

            const result = await validateProKey('test-pro-key-123');

            // Should return Pro status with permanent flag
            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(result.membershipDetails).toEqual(mockValidationResult.membershipDetails);

            // Should have stored permanent status
            expect(chrome.storage.sync.set).toHaveBeenCalledWith(
                expect.objectContaining({
                    'permanentProStatus_abcdef1234567890': expect.objectContaining({
                        isPro: true,
                        verified: true,
                        permanent: true,
                        keyHash: 'abcdef1234567890'
                    })
                })
            );
        });
    });

    describe('Returning Pro User Validation', () => {
        test('should use permanent status without API call', async () => {
            // Setup existing permanent status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': testUtils.createMockProStatus()
                    });
                }
                return Promise.resolve({});
            });

            const result = await validateProKey('test-pro-key-123');

            // Should return permanent status immediately
            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(result.message).toBe('Pro status verified (permanent)');

            // Should NOT call API validation
            const { performValidation } = require('../js/auth/proValidator.js');
            expect(performValidation).not.toHaveBeenCalled();
        });
    });

    describe('Migration Flow', () => {
        test('should migrate existing Pro user from cache to permanent', async () => {
            // Setup existing Pro key and status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123',
                        hustleProStatus: {
                            isPro: true,
                            lastChecked: '2025-01-15T10:30:00.000Z'
                        }
                    });
                }
                return Promise.resolve({});
            });

            // Setup cached Pro status
            chrome.storage.local.get.mockImplementation(() => {
                return Promise.resolve({
                    'proStatus_abcdef1234567890': {
                        isPro: true,
                        lastValidated: '2025-01-15T10:30:00.000Z',
                        membershipDetails: {
                            tier: 'pro',
                            status: 'active',
                            expiresAt: '2026-01-15T10:30:00.000Z'
                        }
                    }
                });
            });

            const result = await migrateCacheToPermanent();

            expect(result.success).toBe(true);
            expect(result.migrated).toBe(true);
            expect(result.migratedFrom).toBe('cache');

            // Should have stored permanent status
            expect(chrome.storage.sync.set).toHaveBeenCalledWith(
                expect.objectContaining({
                    'permanentProStatus_abcdef1234567890': expect.objectContaining({
                        isPro: true,
                        verified: true,
                        permanent: true,
                        migrated: true
                    })
                })
            );
        });
    });

    describe('checkProStatus Integration', () => {
        test('should return permanent status information', async () => {
            // Setup Pro key
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('hustleProKey')) {
                    return Promise.resolve({
                        hustleProKey: 'test-pro-key-123'
                    });
                }
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': testUtils.createMockProStatus()
                    });
                }
                return Promise.resolve({});
            });

            const result = await checkProStatus();

            expect(result.isPro).toBe(true);
            expect(result.permanent).toBe(true);
            expect(result.cached).toBe(false);

            // Should have stored status with permanent flag
            expect(chrome.storage.sync.set).toHaveBeenCalledWith({
                hustleProStatus: expect.objectContaining({
                    isPro: true,
                    permanent: true,
                    migrated: false
                })
            });
        });
    });

    describe('Error Handling', () => {
        test('should handle permanent storage failure gracefully', async () => {
            // Mock storage failure
            chrome.storage.sync.set.mockRejectedValueOnce(new Error('Storage quota exceeded'));

            // Mock successful API validation
            const { performValidation } = require('../js/auth/proValidator.js');
            performValidation.mockResolvedValueOnce({
                isPro: true,
                cached: false,
                message: 'Pro user verified',
                membershipDetails: { tier: 'pro', status: 'active' }
            });

            const result = await validateProKey('test-pro-key-123');

            // Should still return Pro status even if permanent storage fails
            expect(result.isPro).toBe(true);
            expect(result.permanent).toBeUndefined(); // No permanent flag due to storage failure
        });

        test('should handle expired permanent status', async () => {
            // Setup expired permanent status
            chrome.storage.sync.get.mockImplementation((keys) => {
                if (keys.includes('permanentProStatus_abcdef1234567890')) {
                    return Promise.resolve({
                        'permanentProStatus_abcdef1234567890': testUtils.createExpiredProStatus()
                    });
                }
                return Promise.resolve({});
            });

            const result = await checkPermanentProStatus('abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890');

            expect(result.verified).toBe(false);
            expect(result.isPro).toBe(false);
            expect(result.message).toBe('Permanent Pro status expired');
            expect(result.expired).toBe(true);
        });
    });

    describe('Performance Requirements', () => {
        test('should meet <100ms performance target for permanent status check', async () => {
            // Setup permanent status
            chrome.storage.sync.get.mockImplementation(() => {
                return Promise.resolve({
                    'permanentProStatus_abcdef1234567890': testUtils.createMockProStatus()
                });
            });

            const startTime = performance.now();
            await validateProKey('test-pro-key-123');
            const endTime = performance.now();

            // Should complete in under 100ms (target from PRP)
            expect(endTime - startTime).toBeLessThan(100);
        });
    });

    describe('Cross-device Synchronization', () => {
        test('should use chrome.storage.sync for cross-device compatibility', async () => {
            const keyHash = 'abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890';
            const membershipDetails = { tier: 'pro', status: 'active' };

            await storePermanentProStatus(keyHash, membershipDetails);

            // Should use sync storage, not local storage
            expect(chrome.storage.sync.set).toHaveBeenCalled();
            expect(chrome.storage.local.set).not.toHaveBeenCalled();
        });
    });
});
