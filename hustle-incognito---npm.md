## hustle-incognito

![TypeScript icon, indicating that this package has built-in type declarations](https://static-production.npmjs.com/255a118f56f5346b97e56325a1217a16.svg "This package contains built-in TypeScript declarations")

0.1.3 • Public • Published a month ago

-   [Readme](?activeTab=readme)
-   [Code Beta](?activeTab=code)
-   [0 Dependencies](?activeTab=dependencies)
-   [0 Dependents](?activeTab=dependents)
-   [2 Versions](?activeTab=versions)

# Emblem Vault Hustle Incognito SDK

[](#emblem-vault-hustle-incognito-sdk)

> **Power your applications with EmblemVault's AI Agent Hustle API – the secure, intelligent assistant for crypto & web3.**

[![npm version](https://camo.githubusercontent.com/dd5eb07db184c087aa6c349e95073f5ed3acefae77b06d2c23ccc083312913a1/68747470733a2f2f696d672e736869656c64732e696f2f6e706d2f762f687573746c652d696e636f676e69746f2e737667)](https://www.npmjs.com/package/hustle-incognito) [![License](https://camo.githubusercontent.com/6581c31c16c1b13ddc2efb92e2ad69a93ddc4a92fd871ff15d401c4c6c9155a4/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6c6963656e73652d4d49542d626c75652e737667)](https://github.com/EmblemCompany/hustle-incognito/blob/HEAD/LICENSE) [![TypeScript](https://camo.githubusercontent.com/67d06fd1ab1a4b4d6419a3c8282bbe69e9a7e81ea9c934cefb6da29328a3bfd1/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f547970655363726970742d352e342d626c7565)](https://www.typescriptlang.org/) [![Maintainer](https://camo.githubusercontent.com/d24e6cac625cd8dfdfffcd3596ca5a02462323a64e794b68bbff428966c257d8/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6d61696e7461696e65722d67656e6563796265722d626c7565)](https://www.npmjs.com/~genecyber)

## ✨ Build an AI-powered CLI in 10 lines

[](#-build-an-ai-powered-cli-in-10-lines)

import { HustleIncognitoClient } from 'hustle-incognito';

// Create client with your API key
const client \= new HustleIncognitoClient({
  apiKey: process.env.HUSTLE\_API\_KEY
});

// Get a response from the AI
const response \= await client.chat(\[
  { role: 'user', content: 'What can you tell me about the current trending tokens on Solana?' }
\], { vaultId: process.env.VAULT\_ID });

console.log(response.content);

## 🚀 Features

[](#-features)

-   **Three Flexible Modes**: Simple request/response, processed streaming, or raw API output
-   **Intelligent AI Agent**: Access to 20+ built-in crypto & web3 tools
-   **Both Browser & Node.js**: Works seamlessly in any JavaScript environment
-   **Minimal Setup**: Production-ready with sensible defaults
-   **Highly Configurable**: Advanced options when you need them
-   **Built for Testing**: Override pattern allows easy mocking

## 📦 Installation

[](#-installation)

# Using npm
npm install hustle-incognito

# Using yarn
yarn add hustle-incognito

# Using pnpm
pnpm add hustle-incognito

## 🔑 Authentication

[](#-authentication)

Authentication is simple - just provide your API key when initializing the client:

const client \= new HustleIncognitoClient({
  apiKey: 'your-api-key-here',
  // Optional configuration
  hustleApiUrl: 'https://agenthustle.ai', // Defaults to https://agenthustle.ai
  debug: true // Enable verbose logging
});

## 🔍 Usage Modes

[](#-usage-modes)

### 1️⃣ Simple Request/Response

[](#1️⃣-simple-requestresponse)

Perfect for CLI tools or simple applications - send a message, get back a complete response:

// Get a complete response
const response \= await client.chat(\[
  { role: 'user', content: 'Show me the top Solana Tokens this week' }
\], { vaultId: 'my-vault' });

console.log(response.content);
console.log(\`Used ${response.usage?.total\_tokens} tokens\`);

// Tool calls are also available
if (response.toolCalls?.length \> 0) {
  console.log('Agent used these tools:', response.toolCalls);
}

### 2️⃣ Processed Streaming (for interactive UIs)

[](#2️⃣-processed-streaming-for-interactive-uis)

Receive typed, structured chunks for building interactive experiences:

// For UIs with streaming responses
for await (const chunk of client.chatStream({ 
  messages: \[{ role: 'user', content: 'Show me the top Solana tokens this week' }\],
  vaultId: 'my-vault',
  processChunks: true 
})) {
  switch (chunk.type) {
    case 'text':
      ui.appendText(chunk.value);
      break;
    case 'tool\_call':
      ui.showToolInProgress(chunk.value);
      break;
    case 'tool\_result':
      ui.showToolResult(chunk.value);
      break;
    case 'finish':
      ui.complete(chunk.value);
      break;
  }
}

### 3️⃣ Raw API Streaming (maximum control)

[](#3️⃣-raw-api-streaming-maximum-control)

Direct access to the raw API stream format:

// For maximum control and custom processing
for await (const rawChunk of client.rawStream({
  messages: \[{ role: 'user', content: 'Find transactions for address 0x123...' }\],
  vaultId: 'my-vault'
})) {
  // Raw chunks have prefix character and data
  console.log(\`Received ${rawChunk.prefix}: ${rawChunk.raw}\`);
  
  // Process different prefix types
  switch (rawChunk.prefix) {
    case '0': // Text chunk
      console.log('Text:', rawChunk.data);
      break;
    case '9': // Tool call
      console.log('Tool call:', rawChunk.data);
      break;
    case 'a': // Tool result
      console.log('Tool result:', rawChunk.data);
      break;
    case 'f': // Message ID
      console.log('Message ID:', rawChunk.data.messageId);
      break;
    case '2': // Path info
      console.log('Path info:', rawChunk.data);
      break;
    case 'e': // Completion event
    case 'd': // Final data
      console.log('Finished:', rawChunk.data);
      break;
  }
}

## 🛠 Built-in Tools

[](#-built-in-tools)

The Agent Hustle API includes powerful built-in tools that execute automatically on the server. The SDK captures these tool calls and results for you:

### Available Tools

[](#available-tools)

-   **Trading & Swaps**: Token swaps, price quotes, limit orders, DCA strategies
-   **Liquidity Provision**: Add/remove liquidity, manage LP positions
-   **PumpFun Tokens**: Buy, sell, deploy tokens, check graduation status
-   **Security & Analysis**: Rugchecks, token audits, holder analysis
-   **Wallet Management**: Check balances, transfer tokens, deposit SOL

### Multiple Tool Execution

[](#multiple-tool-execution)

The API can execute multiple tools in a single conversation. For example, you can ask for trending tokens and a rugcheck in the same request:

// Request that uses multiple tools
const response \= await client.chat(\[
  { role: 'user', content: 'Check trending tokens and get token details for the top one' }
\], { vaultId: 'my-vault' });

// All tool calls are available in the response
console.log(\`Number of tools used: ${response.toolCalls.length}\`);

// Access individual tool calls by index or filter by tool name
const rugcheckCalls \= response.toolCalls.filter(tool \=> tool.name \=== 'rugcheck');
const trendingCalls \= response.toolCalls.filter(tool \=> tool.name \=== 'birdeye-trending');

// Tool results are also available
console.log('Tool results:', response.toolResults);

For streaming interfaces, you can observe multiple tool calls in real-time:

for await (const chunk of client.chatStream({ 
  messages: \[{ role: 'user', content: 'Check trending tokens and get token details for the top one' }\],
  vaultId: 'my-vault',
  processChunks: true 
})) {
  if (chunk.type \=== 'tool\_call') {
    console.log(\`Tool called: ${chunk.value.name}\`);
    // You can track which tools are being used
  } else if (chunk.type \=== 'tool\_result') {
    console.log(\`Tool result received for: ${chunk.value.tool\_call\_id}\`);
    // Match results to their corresponding tool calls
  }
}

### Accessing Tool Data

[](#accessing-tool-data)

// Get a complete response with tool calls and results
const response \= await client.chat(\[
  { role: 'user', content: 'What is the current price of SOL in USD?' }
\], { vaultId: 'my-vault' });

// Tool calls and results are available in the response
console.log('Tools called:', response.toolCalls);
console.log('Tool results:', response.toolResults);

For streaming interfaces, you can observe tool activity in real-time:

for await (const chunk of client.chatStream({ 
  messages: \[{ role: 'user', content: 'Check the price of SOL in USD' }\],
  vaultId: 'my-vault',
  processChunks: true 
})) {
  if (chunk.type \=== 'tool\_call') {
    console.log('Agent is using tool:', chunk.value.name);
  } else if (chunk.type \=== 'tool\_result') {
    console.log('Tool returned:', chunk.value);
  }
}

## 🧪 Testing Your Integration

[](#-testing-your-integration)

The SDK supports an override pattern for easy testing without making real API calls:

// Mock stream results for testing
const mockResponse \= await client.chat(
  \[{ role: 'user', content: 'Test message' }\],
  { vaultId: 'test-vault' },
  async () \=> ({ content: 'Mocked response', toolCalls: \[\] })
);

// Mock stream chunks for testing UI
for await (const chunk of client.chatStream(
  { messages: \[...\], vaultId: 'test-vault' },
  async function\* () {
    yield { type: 'text', value: 'Mocked ' };
    yield { type: 'text', value: 'streaming ' };
    yield { type: 'text', value: 'response' };
    yield { type: 'finish', value: { reason: 'stop' } };
  }
)) {
  // Process mocked chunks in tests
}

## 🔐 Security

[](#-security)

-   Never hardcode API keys in your client code
-   Use environment variables for sensitive credentials
-   For browser applications, proxy requests through your backend

## 📚 Usage in Different Environments

[](#-usage-in-different-environments)

### Node.js (CommonJS)

[](#nodejs-commonjs)

const { HustleIncognitoClient } \= require('hustle-incognito');

const client \= new HustleIncognitoClient({ apiKey: process.env.HUSTLE\_API\_KEY });
// Use the client...

### Node.js (ESM) / Modern JavaScript

[](#nodejs-esm--modern-javascript)

import { HustleIncognitoClient } from 'hustle-incognito';

const client \= new HustleIncognitoClient({ apiKey: process.env.HUSTLE\_API\_KEY });
// Use the client...

### TypeScript

[](#typescript)

import { HustleIncognitoClient, ChatMessage } from 'hustle-incognito';

const client \= new HustleIncognitoClient({ apiKey: process.env.HUSTLE\_API\_KEY });
const messages: ChatMessage\[\] \= \[{ role: 'user', content: 'Hello' }\];
// Use the client...

### Next.js

[](#nextjs)

// pages/api/hustle.ts
import type { NextApiRequest, NextApiResponse } from 'next';
import { HustleIncognitoClient } from 'hustle-incognito';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const client \= new HustleIncognitoClient({ 
    apiKey: process.env.HUSTLE\_API\_KEY 
  });
  
  const response \= await client.chat(
    req.body.messages,
    { vaultId: req.body.vaultId || 'default' }
  );
  
  res.status(200).json(response);
}

### Browser (via bundler)

[](#browser-via-bundler)

import { HustleIncognitoClient } from 'hustle-incognito';

// NOTE: For security, you should proxy API requests through your backend
// rather than including API keys in client-side code
const client \= new HustleIncognitoClient({ 
  apiKey: 'YOUR\_API\_KEY', // Better to fetch this from your backend
  hustleApiUrl: '/api/hustle-proxy' // Proxy through your backend
});

// Use the client...

## 🛠️ Development

[](#️-development)

### Setup

[](#setup)

# Clone the repository
git clone https://github.com/EmblemCompany/hustle-incognito.git
cd hustle-incognito

# Install dependencies
npm install

# Build the SDK
npm run build

### Testing

[](#testing)

# Run all tests
npm test

# Watch mode for development
npm run test:watch

### Running Examples

[](#running-examples)

# Create a .env file with your API key
echo "HUSTLE\_API\_KEY=your\_api\_key\_here" \> .env
echo "VAULT\_ID=your\_vault\_id" \>> .env

# Run the CLI example
npm run example:cli

### Build Process

[](#build-process)

The SDK uses a dual package approach to support both ESM and CommonJS:

# Build ESM version
npm run build:esm

# Build CommonJS version
npm run build:cjs

# Build both versions
npm run build

### Publishing

[](#publishing)

# Prepare for publishing (runs tests, lint, and build)
npm version patch # or minor, or major
npm publish

## 📄 License

[](#-license)

MIT

## Readme

### Keywords

-   [emblemvault](/search?q=keywords:emblemvault)
-   [hustle](/search?q=keywords:hustle)
-   [incognito](/search?q=keywords:incognito)
-   [ai](/search?q=keywords:ai)
-   [sdk](/search?q=keywords:sdk)
-   [crypto](/search?q=keywords:crypto)
-   [solana](/search?q=keywords:solana)

## Package Sidebar

### Install

`npm i hustle-incognito`

### Repository

[Gitgithub.com/EmblemCompany/hustle-incognito](https://github.com/EmblemCompany/hustle-incognito)

### Homepage

[github.com/EmblemCompany/hustle-incognito#readme](https://github.com/EmblemCompany/hustle-incognito#readme)

### DownloadsWeekly Downloads

15

### Version

0.1.3

### License

MIT

### Unpacked Size

95.3 kB

### Total Files

26

### Last publish

a month ago

### Collaborators

-   [![genecyber](/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci9iNzViM2Q4YTEyZGY4MGRmNzI0NmYzN2Y2ODZkMWFmOT9zaXplPTEwMCZkZWZhdWx0PXJldHJvIn0.WBitbaeE0eqDS_GavVa2bG7VlDHjNfHPqwk2T_JekFs "genecyber")](/~genecyber)
    

[**Try** on RunKit](https://runkit.com/npm/hustle-incognito)

[**Report** malware](/support?inquire=security&security-inquire=malware&package=hustle-incognito&version=0.1.3)